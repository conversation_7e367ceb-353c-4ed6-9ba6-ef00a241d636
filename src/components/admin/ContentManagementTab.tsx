import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Mountain, FileText, MessageSquare } from 'lucide-react';
import AdminTreks from './AdminTreks';
import AdminBlogs from './AdminBlogs';
import AdminTestimonials from './AdminTestimonials';

interface ContentManagementTabProps {
  onStatsChange: () => void;
}

const ContentManagementTab: React.FC<ContentManagementTabProps> = ({ onStatsChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Content Management</h2>
        <p className="text-muted-foreground">
          Manage all your website content including trek packages, blog posts, and testimonials
        </p>
      </div>

      <Tabs defaultValue="treks" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="treks" className="flex items-center gap-2">
            <Mountain className="h-4 w-4" />
            Trek Packages
          </TabsTrigger>
          <TabsTrigger value="blogs" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Blog Posts
          </TabsTrigger>
          <TabsTrigger value="testimonials" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Testimonials
          </TabsTrigger>
        </TabsList>

        <TabsContent value="treks">
          <AdminTreks onStatsChange={onStatsChange} />
        </TabsContent>

        <TabsContent value="blogs">
          <AdminBlogs onStatsChange={onStatsChange} />
        </TabsContent>

        <TabsContent value="testimonials">
          <AdminTestimonials onStatsChange={onStatsChange} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContentManagementTab;
