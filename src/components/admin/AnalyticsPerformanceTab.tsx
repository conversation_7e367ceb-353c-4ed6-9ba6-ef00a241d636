import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  Users,
  Eye,
  Clock,
  RefreshCw,
  FileText,
  Mountain,
  MessageSquare,
  Calendar,
  Target,
  Activity
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { dynamicAnalyticsService, ContentMetrics, TopContent, RecentActivity } from '@/lib/analytics/dynamic-analytics-service';
import { analyticsSeeder } from '@/lib/analytics/analytics-seeder';

// Remove duplicate interfaces - using ones from dynamic-analytics-service

const AnalyticsPerformanceTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [contentMetrics, setContentMetrics] = useState<ContentMetrics>({
    totalBlogs: 0,
    totalTreks: 0,
    totalBookings: 0,
    totalTestimonials: 0,
    publishedThisMonth: 0,
    optimizedContent: 0,
    totalViews: 0,
    totalEngagement: 0
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [topContent, setTopContent] = useState<TopContent[]>([]);

  const loadAnalyticsData = useCallback(async () => {
    setLoading(true);
    try {
      // Ensure analytics data is seeded
      await analyticsSeeder.seedIfEmpty();

      // Load all analytics data using dynamic service
      const [contentMetricsData, topContentData, recentActivityData] = await Promise.all([
        dynamicAnalyticsService.getContentMetrics(),
        dynamicAnalyticsService.getTopContent(10),
        dynamicAnalyticsService.getRecentActivity(15)
      ]);

      setContentMetrics(contentMetricsData);
      setTopContent(topContentData);
      setRecentActivity(recentActivityData);

    } catch (error) {
      console.error('Failed to load analytics data:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadAnalyticsData();
    const interval = setInterval(loadAnalyticsData, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, [loadAnalyticsData]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'blog': return <FileText className="h-4 w-4" />;
      case 'trek': return <Mountain className="h-4 w-4" />;
      case 'booking': return <Users className="h-4 w-4" />;
      case 'testimonial': return <MessageSquare className="h-4 w-4" />;
      case 'optimization': return <Target className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'success': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <BarChart3 className="h-8 w-8" />
            Analytics & Performance
          </h2>
          <p className="text-muted-foreground">
            Monitor content performance, user engagement, and system metrics
          </p>
        </div>
        <Button onClick={loadAnalyticsData} variant="outline" size="sm" disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Content</p>
                <p className="text-2xl font-bold">{contentMetrics.totalBlogs + contentMetrics.totalTreks}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {contentMetrics.totalBlogs} blogs, {contentMetrics.totalTreks} treks
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Published This Month</p>
                <p className="text-2xl font-bold text-green-600">{contentMetrics.publishedThisMonth}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                New content published
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">SEO Optimized</p>
                <p className="text-2xl font-bold text-purple-600">{contentMetrics.optimizedContent}</p>
              </div>
              <Target className="h-8 w-8 text-purple-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                AI-optimized content
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Inquiries</p>
                <p className="text-2xl font-bold text-orange-600">{contentMetrics.totalBookings}</p>
              </div>
              <Users className="h-8 w-8 text-orange-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                Customer inquiries
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content Performance</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Content Growth
                </CardTitle>
                <CardDescription>Content creation and optimization trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Blog Posts</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${Math.min((contentMetrics.totalBlogs / 50) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{contentMetrics.totalBlogs}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Trek Packages</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${Math.min((contentMetrics.totalTreks / 20) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{contentMetrics.totalTreks}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">SEO Optimized</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full"
                          style={{ width: `${Math.min((contentMetrics.optimizedContent / contentMetrics.totalBlogs) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{contentMetrics.optimizedContent}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Top Performing Content
                </CardTitle>
                <CardDescription>Most viewed content this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {topContent.slice(0, 5).map((content, index) => (
                    <div key={content.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                        {content.type === 'blog' ? (
                          <FileText className="h-4 w-4 text-blue-500" />
                        ) : (
                          <Mountain className="h-4 w-4 text-green-500" />
                        )}
                        <span className="text-sm truncate max-w-48">{content.title}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{content.views} views</Badge>
                        <Badge variant="secondary">{content.engagement}% engagement</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Blog Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Posts</span>
                    <Badge variant="outline">{contentMetrics.totalBlogs}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Published</span>
                    <Badge variant="outline" className="text-green-600">
                      {contentMetrics.totalBlogs - (contentMetrics.totalBlogs * 0.1)}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">SEO Optimized</span>
                    <Badge variant="outline" className="text-purple-600">{contentMetrics.optimizedContent}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mountain className="h-5 w-5" />
                  Trek Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Packages</span>
                    <Badge variant="outline">{contentMetrics.totalTreks}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Featured</span>
                    <Badge variant="outline" className="text-blue-600">
                      {Math.floor(contentMetrics.totalTreks * 0.3)}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg. Views</span>
                    <Badge variant="outline">1,250</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Engagement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Inquiries</span>
                    <Badge variant="outline">{contentMetrics.totalBookings}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Testimonials</span>
                    <Badge variant="outline" className="text-green-600">{contentMetrics.totalTestimonials}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Conversion Rate</span>
                    <Badge variant="outline">3.2%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>Latest content updates and user interactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getActivityIcon(activity.type)}
                      <div>
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-muted-foreground">{formatDate(activity.date)}</p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(activity.status)}>
                      {activity.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsPerformanceTab;
