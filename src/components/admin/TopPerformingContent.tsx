import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, TrendingUp, FileText, Mountain, RefreshCw } from 'lucide-react';
import { dynamicAnalyticsService, TopContent } from '@/lib/analytics/dynamic-analytics-service';
import { toast } from '@/hooks/use-toast';

interface TopPerformingContentProps {
  limit?: number;
  showRefresh?: boolean;
  className?: string;
}

const TopPerformingContent: React.FC<TopPerformingContentProps> = ({
  limit = 5,
  showRefresh = true,
  className = ""
}) => {
  const [topContent, setTopContent] = useState<TopContent[]>([]);
  const [loading, setLoading] = useState(true);

  const loadTopContent = async () => {
    setLoading(true);
    try {
      const data = await dynamicAnalyticsService.getTopContent(limit);
      setTopContent(data);
    } catch (error) {
      console.error('Failed to load top content:', error);
      toast({
        title: "Error",
        description: "Failed to load top performing content",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTopContent();
  }, [limit]);

  const getEngagementBadge = (engagement: number) => {
    if (engagement >= 80) return { variant: "default" as const, text: `${engagement}% engagement` };
    if (engagement >= 60) return { variant: "secondary" as const, text: `${engagement}% engagement` };
    return { variant: "outline" as const, text: `${engagement}% engagement` };
  };

  const formatViews = (views: number) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}k views`;
    }
    return `${views} views`;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Top Performing Content
          </CardTitle>
          <CardDescription>Most viewed content this month</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(limit)].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-gray-200"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-48"></div>
                    <div className="h-3 bg-gray-200 rounded w-32"></div>
                  </div>
                </div>
                <div className="w-16 h-8 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Top Performing Content
            </CardTitle>
            <CardDescription>Most viewed content this month</CardDescription>
          </div>
          {showRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={loadTopContent}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {topContent.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No content performance data available yet.</p>
              <p className="text-sm">Content will appear here once analytics data is collected.</p>
            </div>
          ) : (
            topContent.map((content, index) => (
              <div key={content.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                    #{index + 1}
                  </div>
                  <div className="flex items-center gap-2">
                    {content.type === 'blog' ? (
                      <FileText className="h-4 w-4 text-blue-500" />
                    ) : (
                      <Mountain className="h-4 w-4 text-green-500" />
                    )}
                    <div>
                      <h4 className="font-medium text-sm leading-tight">{content.title}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">
                          {formatViews(content.views)}
                        </span>
                        <Badge
                          variant={getEngagementBadge(content.engagement).variant}
                          className="text-xs"
                        >
                          {getEngagementBadge(content.engagement).text}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TopPerformingContent;
