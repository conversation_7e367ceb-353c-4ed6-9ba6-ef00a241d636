import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Users } from 'lucide-react';
import AdminBookings from './AdminBookings';

interface BookingsInquiriesTabProps {
  onStatsChange: () => void;
}

const BookingsInquiriesTab: React.FC<BookingsInquiriesTabProps> = ({ onStatsChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Users className="h-8 w-8" />
          Bookings & Inquiries
        </h2>
        <p className="text-muted-foreground">
          Manage customer inquiries, booking requests, and communication
        </p>
      </div>

      <AdminBookings onStatsChange={onStatsChange} />
    </div>
  );
};

export default BookingsInquiriesTab;
