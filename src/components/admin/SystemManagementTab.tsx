import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Shield,
  Database,
  Cloud,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Activity,
  Trash2,
  Download,
  Settings,
  Zap,
  Clock,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { errorService, ErrorLog } from '@/lib/error-handling/error-service';
import { logger, LogEntry } from '@/lib/logging/logger';
import { dynamicAnalyticsService, SystemHealth as DynamicSystemHealth, PerformanceMetrics } from '@/lib/analytics/dynamic-analytics-service';
import { analyticsSeeder } from '@/lib/analytics/analytics-seeder';

interface SystemMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
}

const SystemManagementTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState('health');
  const [loading, setLoading] = useState(false);
  const [health, setHealth] = useState<DynamicSystemHealth | null>(null);
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [errors, setErrors] = useState<ErrorLog[]>([]);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [errorStats, setErrorStats] = useState(errorService.getStats());
  const [logStats, setLogStats] = useState(logger.getStats());

  const checkSystemHealth = useCallback(async () => {
    setLoading(true);
    try {
      // Ensure analytics data is seeded
      await analyticsSeeder.seedIfEmpty();

      // Get system health and performance metrics from dynamic service
      const [systemHealth, performanceMetrics] = await Promise.all([
        dynamicAnalyticsService.getSystemHealth(),
        dynamicAnalyticsService.getPerformanceMetrics()
      ]);

      setHealth(systemHealth);

      // Convert performance metrics to system metrics format
      const systemMetrics: SystemMetrics = {
        totalRequests: performanceMetrics.totalRequests,
        successfulRequests: performanceMetrics.successfulRequests,
        failedRequests: performanceMetrics.failedRequests,
        averageResponseTime: performanceMetrics.avgResponseTime,
        errorRate: performanceMetrics.errorRate,
        uptime: performanceMetrics.uptime
      };

      setMetrics(systemMetrics);
      setLastUpdate(new Date());

      // Track this health check
      await dynamicAnalyticsService.trackSystemHealth(
        'overall',
        systemHealth.overall.status,
        undefined,
        `System health check completed at ${new Date().toISOString()}`
      );

    } catch (error) {
      console.error('Failed to check system health:', error);
      toast({
        title: "Error",
        description: "Failed to check system health",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Health check functions removed - using dynamic analytics service

  const refreshErrorsAndLogs = useCallback(() => {
    setErrors(errorService.getErrors());
    setLogs(logger.getLogs());
    setErrorStats(errorService.getStats());
    setLogStats(logger.getStats());
  }, []);

  useEffect(() => {
    checkSystemHealth();
    refreshErrorsAndLogs();

    const interval = setInterval(() => {
      checkSystemHealth();
      refreshErrorsAndLogs();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [checkSystemHealth, refreshErrorsAndLogs]);

  const handleResolveError = (errorId: string) => {
    errorService.resolveError(errorId);
    refreshErrorsAndLogs();
    toast({
      title: "Error Resolved",
      description: "Error has been marked as resolved",
    });
  };

  const handleClearErrors = () => {
    errorService.clearErrors();
    refreshErrorsAndLogs();
    toast({
      title: "Errors Cleared",
      description: "All errors have been cleared",
    });
  };

  const handleClearLogs = () => {
    logger.clearLogs();
    refreshErrorsAndLogs();
    toast({
      title: "Logs Cleared",
      description: "All logs have been cleared",
    });
  };

  const handleExportLogs = () => {
    const logsData = {
      errors: errors,
      logs: logs,
      exportedAt: new Date().toISOString(),
      stats: { errorStats, logStats }
    };

    const blob = new Blob([JSON.stringify(logsData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Logs Exported",
      description: "System logs have been downloaded",
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'degraded': return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'unhealthy': return <AlertCircle className="h-5 w-5 text-red-500" />;
      default: return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'degraded': return 'text-yellow-600';
      case 'unhealthy': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="h-8 w-8" />
            System Management
          </h2>
          <p className="text-muted-foreground">
            Monitor system health, manage errors, and view system logs
          </p>
        </div>
        <div className="flex items-center gap-2">
          {health && getStatusIcon(health.overall.status)}
          <span className={`text-sm font-medium ${health ? getStatusColor(health.overall.status) : ''}`}>
            {health ? health.overall.status.charAt(0).toUpperCase() + health.overall.status.slice(1) : 'Checking...'}
          </span>
          <Button onClick={checkSystemHealth} variant="outline" size="sm" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">System Uptime</p>
                <p className="text-2xl font-bold text-green-600">{health?.overall.uptime || 0}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                Last check: {lastUpdate ? formatDate(lastUpdate) : 'Never'}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Error Rate</p>
                <p className="text-2xl font-bold text-red-600">{metrics?.errorRate.toFixed(2) || 0}%</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {errorStats.errors} total errors
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Response Time</p>
                <p className="text-2xl font-bold text-blue-600">{metrics?.averageResponseTime.toFixed(0) || 0}ms</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                Across all services
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold text-purple-600">{metrics?.totalRequests.toLocaleString() || 0}</p>
              </div>
              <Activity className="h-8 w-8 text-purple-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {metrics?.successfulRequests.toLocaleString() || 0} successful
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="health">System Health</TabsTrigger>
          <TabsTrigger value="errors">Error Management</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="health" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Database Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Status</span>
                    <div className="flex items-center gap-2">
                      {health && getStatusIcon(health.database.status)}
                      <span className={`text-sm font-medium ${health ? getStatusColor(health.database.status) : ''}`}>
                        {health?.database.status || 'Unknown'}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Response Time</span>
                    <Badge variant="outline">{health?.database.responseTime || 0}ms</Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {health?.database.details || 'No details available'}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Gemini API Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Status</span>
                    <div className="flex items-center gap-2">
                      {health && getStatusIcon(health.geminiApi.status)}
                      <span className={`text-sm font-medium ${health ? getStatusColor(health.geminiApi.status) : ''}`}>
                        {health?.geminiApi.status || 'Unknown'}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Response Time</span>
                    <Badge variant="outline">{health?.geminiApi.responseTime || 0}ms</Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {health?.geminiApi.details || 'No details available'}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cloud className="h-5 w-5" />
                  Storage Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Status</span>
                    <div className="flex items-center gap-2">
                      {health && getStatusIcon(health.storage.status)}
                      <span className={`text-sm font-medium ${health ? getStatusColor(health.storage.status) : ''}`}>
                        {health?.storage.status || 'Unknown'}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Response Time</span>
                    <Badge variant="outline">{health?.storage.responseTime || 0}ms</Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {health?.storage.details || 'No details available'}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Error Management</CardTitle>
                <CardDescription>
                  Monitor and resolve system errors
                </CardDescription>
              </div>
              <Button onClick={handleClearErrors} variant="outline" size="sm">
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {errors.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
                      <p>No errors found. System is running smoothly!</p>
                    </div>
                  ) : (
                    errors.map((error) => (
                      <div
                        key={error.id}
                        className={`p-4 border rounded-lg ${
                          error.resolved ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={error.resolved ? "outline" : "destructive"}>
                                {error.level}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {formatDate(error.timestamp)}
                              </span>
                            </div>
                            <p className="font-medium">{error.message}</p>
                            {error.context && (
                              <p className="text-sm text-muted-foreground mt-1">
                                Context: {typeof error.context === 'string' ? error.context : JSON.stringify(error.context)}
                              </p>
                            )}
                            {error.stack && (
                              <details className="mt-2">
                                <summary className="text-sm cursor-pointer">Stack trace</summary>
                                <pre className="text-xs mt-1 p-2 bg-gray-100 rounded overflow-x-auto">
                                  {error.stack}
                                </pre>
                              </details>
                            )}
                          </div>
                          {!error.resolved && (
                            <Button
                              onClick={() => handleResolveError(error.id)}
                              variant="outline"
                              size="sm"
                            >
                              Resolve
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>System Logs</CardTitle>
                <CardDescription>
                  View detailed system activity logs
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button onClick={handleExportLogs} variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button onClick={handleClearLogs} variant="outline" size="sm">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {logs.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Activity className="w-12 h-12 mx-auto mb-4" />
                      <p>No logs available</p>
                    </div>
                  ) : (
                    logs.map((log, index) => (
                      <div
                        key={`log-${index}`}
                        className="p-3 border rounded text-sm"
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant={
                            String(log.level) === 'error' ? 'destructive' :
                            String(log.level) === 'warn' ? 'secondary' :
                            String(log.level) === 'info' ? 'default' : 'outline'
                          }>
                            {log.level}
                          </Badge>
                          <span className="text-muted-foreground">
                            {formatDate(log.timestamp)}
                          </span>
                          {log.component && (
                            <Badge variant="outline">{log.component}</Badge>
                          )}
                        </div>
                        <p>{log.message}</p>
                        {(log as any).data && (
                          <details className="mt-1">
                            <summary className="cursor-pointer text-muted-foreground">
                              Additional data
                            </summary>
                            <pre className="text-xs mt-1 p-2 bg-gray-100 rounded overflow-x-auto">
                              {JSON.stringify((log as any).data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                System Settings
              </CardTitle>
              <CardDescription>
                Configure system monitoring and logging preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Auto Health Checks</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically check system health every 30 seconds
                    </p>
                  </div>
                  <Badge variant="outline" className="text-green-600">Enabled</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Error Notifications</h4>
                    <p className="text-sm text-muted-foreground">
                      Show toast notifications for new errors
                    </p>
                  </div>
                  <Badge variant="outline" className="text-green-600">Enabled</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Log Retention</h4>
                    <p className="text-sm text-muted-foreground">
                      Keep logs for 7 days before automatic cleanup
                    </p>
                  </div>
                  <Badge variant="outline">7 days</Badge>
                </div>
              </div>

              <div className="border-t pt-4">
                <Button onClick={() => {
                  toast({
                    title: "Settings Saved",
                    description: "System settings have been updated",
                  });
                }}>
                  Save Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SystemManagementTab;
