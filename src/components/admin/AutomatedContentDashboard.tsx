/**
 * Automated Content Management Dashboard
 * Central control panel for automated content generation and SEO optimization
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Brain,
  Zap,
  FileText,
  TrendingUp,
  <PERSON>tings,
  Play,
  Pause,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  Sparkles,
  BarChart3,
  Search,
  PenTool,
  Cog,
  Calendar,
  Image,
  Database,
  Activity,
  Loader2,
  Plus,
  Eye,
  Edit,
  Trash2,
  Download,
  Upload
} from 'lucide-react';
import { automatedContentGenerator } from '@/lib/content/automated-content-generator';
import { backgroundSEOService } from '@/lib/seo/background-seo-service';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface ContentGenerationStats {
  totalGenerated: number;
  pendingGeneration: number;
  successRate: number;
  lastGenerated: Date | null;
}

interface SEOOptimizationStats {
  totalOptimized: number;
  pendingOptimization: number;
  averageScore: number;
  lastOptimized: Date | null;
}

interface ContentCalendarItem {
  id: string;
  topic: string;
  keywords: string[];
  scheduledDate: Date;
  status: 'planned' | 'generating' | 'ready' | 'published';
  priority: 'high' | 'medium' | 'low';
  contentType: 'blog' | 'trek' | 'guide';
}

interface TrekGenerationRequest {
  name: string;
  region: string;
  difficulty: string;
  duration: number;
  maxAltitude: number;
  season: string;
  highlights: string[];
}

interface AnalyticsData {
  totalViews: number;
  organicTraffic: number;
  conversionRate: number;
  topPerformingContent: Array<{
    id: string;
    title: string;
    views: number;
    conversions: number;
  }>;
}

export default function AutomatedContentDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isGenerating, setIsGenerating] = useState(false);
  const [contentStats, setContentStats] = useState<ContentGenerationStats>({
    totalGenerated: 0,
    pendingGeneration: 0,
    successRate: 0,
    lastGenerated: null
  });
  const [seoStats, setSeoStats] = useState<SEOOptimizationStats>({
    totalOptimized: 0,
    pendingOptimization: 0,
    averageScore: 0,
    lastOptimized: null
  });
  const [queueStatus, setQueueStatus] = useState({
    totalJobs: 0,
    pendingJobs: 0,
    processingJobs: 0,
    completedJobs: 0,
    failedJobs: 0,
    isProcessing: false
  });

  // Settings
  const [autoGenerationEnabled, setAutoGenerationEnabled] = useState(false);
  const [autoOptimizationEnabled, setAutoOptimizationEnabled] = useState(true);
  const [generationInterval, setGenerationInterval] = useState(24); // hours
  const [postsPerBatch, setPostsPerBatch] = useState(3);

  // Enhanced features state
  const [contentCalendar, setContentCalendar] = useState<ContentCalendarItem[]>([]);
  const [isGeneratingTrek, setIsGeneratingTrek] = useState(false);
  const [trekRequest, setTrekRequest] = useState<TrekGenerationRequest>({
    name: '',
    region: '',
    difficulty: 'moderate',
    duration: 14,
    maxAltitude: 5000,
    season: 'spring',
    highlights: []
  });
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalViews: 0,
    organicTraffic: 0,
    conversionRate: 0,
    topPerformingContent: []
  });
  const [recentActivity, setRecentActivity] = useState<Array<{
    id: string;
    type: 'generation' | 'optimization' | 'publication';
    description: string;
    timestamp: Date;
    status: 'success' | 'error' | 'pending';
  }>>([]);
  const [systemHealth, setSystemHealth] = useState({
    geminiApi: 'healthy',
    supabaseConnection: 'healthy',
    queueProcessor: 'healthy',
    lastHealthCheck: new Date()
  });

  const addActivity = (type: 'generation' | 'optimization' | 'publication', description: string, status: 'success' | 'error' | 'pending') => {
    const newActivity = {
      id: Date.now().toString(),
      type,
      description,
      timestamp: new Date(),
      status
    };
    setRecentActivity(prev => [newActivity, ...prev.slice(0, 9)]);
  };

  const loadDashboardData = useCallback(async () => {
    try {
      // Get SEO queue status
      const status = backgroundSEOService.getQueueStatus();
      setQueueStatus(status);

      // Load real data from Supabase
      await Promise.all([
        loadContentStats(),
        loadSEOStats(),
        loadContentCalendar(),
        loadAnalyticsData(),
        loadRecentActivity(),
        checkSystemHealth()
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      addActivity('optimization', 'Failed to load dashboard data', 'error');
    }
  }, []);

  const loadContentStats = async () => {
    try {
      const { data: posts, error } = await supabase
        .from('blog_posts')
        .select('id, created_at')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const today = new Date();
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const recentPosts = posts?.filter(post => new Date(post.created_at) > lastWeek) || [];
      const weeklyGenerated = recentPosts.length;

      setContentStats({
        totalGenerated: posts?.length || 0,
        pendingGeneration: automatedContentGenerator.isGeneratingContent() ? 1 : 0,
        successRate: 95, // This would be calculated from actual success/failure data
        lastGenerated: posts?.[0] ? new Date(posts[0].created_at) : null
      });
    } catch (error) {
      console.error('Failed to load content stats:', error);
    }
  };

  const loadSEOStats = async () => {
    try {
      const { data: logs, error } = await supabase
        .from('seo_optimization_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      const successfulOptimizations = logs?.filter(log => log.status === 'success') || [];
      const averageScore = successfulOptimizations.length > 0
        ? successfulOptimizations.reduce((sum, log) => sum + (log.gemini_response?.seoScore || 0), 0) / successfulOptimizations.length
        : 0;

      setSeoStats({
        totalOptimized: successfulOptimizations.length,
        pendingOptimization: queueStatus.pendingJobs,
        averageScore: Math.round(averageScore * 10) / 10,
        lastOptimized: logs?.[0] ? new Date(logs[0].created_at) : null
      });
    } catch (error) {
      console.error('Failed to load SEO stats:', error);
    }
  };

  const loadContentCalendar = async () => {
    try {
      const { data: calendar, error } = await supabase
        .from('content_calendar')
        .select('*')
        .order('suggested_date', { ascending: true })
        .limit(20);

      if (error) throw error;

      const calendarItems: ContentCalendarItem[] = calendar?.map(item => ({
        id: item.id,
        topic: item.topic,
        keywords: item.keywords || [],
        scheduledDate: new Date(item.suggested_date),
        status: item.status === 'published' ? 'published' : 'planned',
        priority: item.ai_reasoning?.includes('high') ? 'high' : 'medium',
        contentType: 'blog'
      })) || [];

      setContentCalendar(calendarItems);
    } catch (error) {
      console.error('Failed to load content calendar:', error);
    }
  };

  const loadAnalyticsData = async () => {
    // In a real implementation, this would fetch from analytics service
    // For now, we'll use mock data
    setAnalyticsData({
      totalViews: 15420,
      organicTraffic: 8930,
      conversionRate: 3.2,
      topPerformingContent: [
        { id: '1', title: 'Everest Base Camp Trek Guide', views: 2340, conversions: 45 },
        { id: '2', title: 'Annapurna Circuit Complete Guide', views: 1890, conversions: 38 },
        { id: '3', title: 'Best Time to Trek in Nepal', views: 1650, conversions: 32 }
      ]
    });
  };

  const loadRecentActivity = async () => {
    try {
      const { data: logs, error } = await supabase
        .from('seo_optimization_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      const activities = logs?.map(log => ({
        id: log.id,
        type: 'optimization' as const,
        description: `SEO optimization ${log.status} for ${log.original_title || 'content'}`,
        timestamp: new Date(log.created_at),
        status: log.status === 'success' ? 'success' as const : 'error' as const
      })) || [];

      setRecentActivity(activities);
    } catch (error) {
      console.error('Failed to load recent activity:', error);
    }
  };

  const checkSystemHealth = async () => {
    try {
      // Check Supabase connection
      const { error: supabaseError } = await supabase.from('blog_posts').select('id').limit(1);

      // Check Gemini API (this would be done through a health check endpoint)
      const geminiHealth = 'healthy'; // Placeholder

      setSystemHealth({
        geminiApi: geminiHealth,
        supabaseConnection: supabaseError ? 'unhealthy' : 'healthy',
        queueProcessor: queueStatus.isProcessing ? 'healthy' : 'idle',
        lastHealthCheck: new Date()
      });
    } catch (error) {
      console.error('Health check failed:', error);
    }
  };

  const handleGenerateContent = async () => {
    if (isGenerating) return;

    setIsGenerating(true);
    try {
      toast({
        title: "🚀 Starting content generation",
        description: `Generating ${postsPerBatch} blog posts automatically...`,
      });

      const generatedIds = await automatedContentGenerator.generateContentBatch(postsPerBatch);

      setContentStats(prev => ({
        ...prev,
        totalGenerated: prev.totalGenerated + generatedIds.length,
        lastGenerated: new Date(),
        successRate: 95 // This would be calculated from actual data
      }));

      toast({
        title: "✅ Content generation complete",
        description: `Successfully generated ${generatedIds.length} blog posts.`,
      });
    } catch (error) {
      toast({
        title: "❌ Content generation failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleOptimizeAllContent = async () => {
    try {
      toast({
        title: "🔄 Starting SEO optimization",
        description: "Queuing all content for SEO optimization...",
      });

      const queuedCount = await backgroundSEOService.optimizeAllContent();

      toast({
        title: "✅ SEO optimization queued",
        description: `${queuedCount} items queued for optimization.`,
      });

      loadDashboardData();
    } catch (error) {
      toast({
        title: "❌ SEO optimization failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    }
  };

  const handleAnalyzeContent = async () => {
    try {
      toast({
        title: "📊 Analyzing content",
        description: "Analyzing existing content for optimization opportunities...",
      });

      const analysis = await automatedContentGenerator.analyzeExistingContent();

      toast({
        title: "✅ Content analysis complete",
        description: `Found ${analysis.contentGaps.length} content gaps and ${analysis.needsOptimization.length} posts needing optimization.`,
      });
    } catch (error) {
      toast({
        title: "❌ Content analysis failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleGenerateTrek = async () => {
    if (isGeneratingTrek || !trekRequest.name || !trekRequest.region) {
      toast({
        title: "❌ Invalid trek data",
        description: "Please fill in trek name and region",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingTrek(true);
    addActivity('generation', `Generating trek package: ${trekRequest.name}`, 'pending');

    try {
      // Generate trek package using AI
      const trekData = await generateTrekPackage(trekRequest);

      // Save to database
      const { data, error } = await supabase
        .from('trek_packages')
        .insert([trekData])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "✅ Trek package generated",
        description: `Successfully created ${trekRequest.name} trek package`,
      });

      addActivity('generation', `Trek package created: ${trekRequest.name}`, 'success');

      // Reset form
      setTrekRequest({
        name: '',
        region: '',
        difficulty: 'moderate',
        duration: 14,
        maxAltitude: 5000,
        season: 'spring',
        highlights: []
      });

      // Refresh data
      loadDashboardData();
    } catch (error) {
      toast({
        title: "❌ Trek generation failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
      addActivity('generation', `Trek generation failed: ${trekRequest.name}`, 'error');
    } finally {
      setIsGeneratingTrek(false);
    }
  };

  const generateTrekPackage = async (request: TrekGenerationRequest) => {
    const prompt = `Generate a comprehensive Nepal trek package based on these requirements:

Trek Name: ${request.name}
Region: ${request.region}
Difficulty: ${request.difficulty}
Duration: ${request.duration} days
Max Altitude: ${request.maxAltitude}m
Best Season: ${request.season}
Highlights: ${request.highlights.join(', ')}

Generate a complete trek package with:
1. Detailed itinerary (day by day)
2. Comprehensive description
3. What's included/excluded
4. Difficulty assessment
5. Packing list recommendations
6. Best time to visit details
7. Pricing structure

Return ONLY a valid JSON object with the trek package data matching the database schema.`;

    // This would use the Gemini service to generate the trek package
    // For now, return a structured object
    return {
      name: request.name,
      slug: request.name.toLowerCase().replace(/\s+/g, '-'),
      region: request.region,
      short_description: `Experience the ${request.name} trek in ${request.region}, a ${request.difficulty} ${request.duration}-day adventure reaching ${request.maxAltitude}m altitude.`,
      long_description: `The ${request.name} trek is one of Nepal's premier trekking experiences, offering stunning mountain views, cultural immersion, and unforgettable adventures in the ${request.region} region.`,
      itinerary: generateItinerary(request.duration),
      gallery: [],
      featured: false,
      price_usd: calculateTrekPrice(request.duration, request.difficulty),
      duration_days: request.duration,
      minimum_days: Math.max(1, request.duration - 2),
      difficulty_level: request.difficulty,
      max_altitude: request.maxAltitude,
      best_season: request.season,
      duration_variations: {
        variations: [
          { days: request.duration, price: calculateTrekPrice(request.duration, request.difficulty) },
          { days: request.duration + 2, price: calculateTrekPrice(request.duration + 2, request.difficulty) }
        ]
      }
    };
  };

  const generateItinerary = (days: number) => {
    const itinerary = [];
    for (let i = 1; i <= days; i++) {
      itinerary.push({
        day: i,
        title: `Day ${i}`,
        description: `Trek itinerary for day ${i}`,
        altitude: 3000 + (i * 200),
        duration: "6-8 hours",
        accommodation: i === 1 || i === days ? "Hotel" : "Tea House"
      });
    }
    return itinerary;
  };

  const calculateTrekPrice = (days: number, difficulty: string) => {
    const basePrice = 50; // Base price per day
    const difficultyMultiplier = {
      'easy': 1.0,
      'moderate': 1.2,
      'challenging': 1.5,
      'strenuous': 1.8
    };
    return Math.round(days * basePrice * (difficultyMultiplier[difficulty as keyof typeof difficultyMultiplier] || 1.2));
  };

  const handleScheduleContent = async (topic: string, date: Date) => {
    try {
      const { data, error } = await supabase
        .from('content_calendar')
        .insert([{
          topic,
          keywords: [topic.toLowerCase().replace(/\s+/g, '-')],
          seasonality_score: 0.8,
          predicted_engagement: 0.7,
          status: 'suggested',
          ai_reasoning: `Scheduled content for ${topic}`,
          suggested_date: date.toISOString()
        }]);

      if (error) throw error;

      toast({
        title: "📅 Content scheduled",
        description: `${topic} has been added to the content calendar`,
      });

      loadContentCalendar();
    } catch (error) {
      toast({
        title: "❌ Scheduling failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    }
  };

  // Initialize dashboard data
  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [loadDashboardData]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Automated Content Management</h2>
          <p className="text-muted-foreground">
            AI-powered content generation and SEO optimization for your Nepal adventure platform
          </p>
        </div>
        <div className="flex items-center gap-2">
          {queueStatus.isProcessing && (
            <Badge variant="secondary" className="animate-pulse">
              <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
              Processing
            </Badge>
          )}
          {isGenerating && (
            <Badge variant="secondary" className="animate-pulse">
              <PenTool className="mr-1 h-3 w-3" />
              Generating
            </Badge>
          )}
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Generated Posts</p>
                <p className="text-2xl font-bold">{contentStats.totalGenerated}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">SEO Optimized</p>
                <p className="text-2xl font-bold">{seoStats.totalOptimized}</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Queue Status</p>
                <p className="text-2xl font-bold">{queueStatus.pendingJobs}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">{contentStats.successRate}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Blog Content</TabsTrigger>
          <TabsTrigger value="treks">Trek Generation</TabsTrigger>
          <TabsTrigger value="calendar">Content Calendar</TabsTrigger>
          <TabsTrigger value="seo">SEO Optimization</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Content Generation Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-generation:</span>
                  {getStatusIcon(autoGenerationEnabled ? 'healthy' : 'warning')}
                  <span className="text-sm">{autoGenerationEnabled ? 'Enabled' : 'Disabled'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Last generated:</span>
                  <span className="text-sm text-muted-foreground">
                    {contentStats.lastGenerated ? contentStats.lastGenerated.toLocaleDateString() : 'Never'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Success rate:</span>
                  <span className="text-sm font-medium">{contentStats.successRate}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  SEO Optimization Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-optimization:</span>
                  {getStatusIcon(autoOptimizationEnabled ? 'healthy' : 'warning')}
                  <span className="text-sm">{autoOptimizationEnabled ? 'Enabled' : 'Disabled'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Queue status:</span>
                  <span className="text-sm text-muted-foreground">
                    {queueStatus.pendingJobs} pending, {queueStatus.processingJobs} processing
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Average score:</span>
                  <span className="text-sm font-medium">{seoStats.averageScore}/10</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={handleGenerateContent}
                  disabled={isGenerating}
                  className="h-20 flex-col gap-2"
                >
                  {isGenerating ? (
                    <RefreshCw className="h-6 w-6 animate-spin" />
                  ) : (
                    <Sparkles className="h-6 w-6" />
                  )}
                  Generate Content
                </Button>

                <Button
                  onClick={handleOptimizeAllContent}
                  variant="outline"
                  className="h-20 flex-col gap-2"
                >
                  <Target className="h-6 w-6" />
                  Optimize All SEO
                </Button>

                <Button
                  onClick={handleAnalyzeContent}
                  variant="outline"
                  className="h-20 flex-col gap-2"
                >
                  <BarChart3 className="h-6 w-6" />
                  Analyze Content
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PenTool className="h-5 w-5" />
                Content Generation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Button
                  onClick={handleGenerateContent}
                  disabled={isGenerating}
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate {postsPerBatch} Blog Posts
                    </>
                  )}
                </Button>
                <div className="text-sm text-muted-foreground">
                  {isGenerating ? 'AI is researching and writing content...' : 'Ready to generate content'}
                </div>
              </div>

              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Generating content...</span>
                    <span>Please wait</span>
                  </div>
                  <Progress value={undefined} className="h-2" />
                </div>
              )}

              <Alert>
                <Brain className="h-4 w-4" />
                <AlertDescription>
                  AI will research trending topics, analyze content gaps, and generate high-quality blog posts
                  optimized for Nepal adventure tourism keywords.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                SEO Optimization Queue
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{queueStatus.pendingJobs}</div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{queueStatus.processingJobs}</div>
                  <div className="text-sm text-muted-foreground">Processing</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{queueStatus.completedJobs}</div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{queueStatus.failedJobs}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleOptimizeAllContent} className="flex-1">
                  <Target className="mr-2 h-4 w-4" />
                  Optimize All Content
                </Button>
                <Button
                  onClick={() => backgroundSEOService.clearCompletedJobs()}
                  variant="outline"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Clear Completed
                </Button>
              </div>

              {queueStatus.isProcessing && (
                <Alert>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <AlertDescription>
                    SEO optimization is running in the background. Content will be automatically optimized and updated.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Automation Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-generation">Auto Content Generation</Label>
                    <div className="text-sm text-muted-foreground">
                      Automatically generate new blog posts
                    </div>
                  </div>
                  <Switch
                    id="auto-generation"
                    checked={autoGenerationEnabled}
                    onCheckedChange={setAutoGenerationEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-seo">Auto SEO Optimization</Label>
                    <div className="text-sm text-muted-foreground">
                      Automatically optimize content for SEO
                    </div>
                  </div>
                  <Switch
                    id="auto-seo"
                    checked={autoOptimizationEnabled}
                    onCheckedChange={setAutoOptimizationEnabled}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="generation-interval">Generation Interval (hours)</Label>
                  <Input
                    id="generation-interval"
                    type="number"
                    value={generationInterval}
                    onChange={(e) => setGenerationInterval(Number(e.target.value))}
                    min="1"
                    max="168"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="posts-per-batch">Posts per Batch</Label>
                  <Input
                    id="posts-per-batch"
                    type="number"
                    value={postsPerBatch}
                    onChange={(e) => setPostsPerBatch(Number(e.target.value))}
                    min="1"
                    max="10"
                  />
                </div>
              </div>

              <Alert>
                <Cog className="h-4 w-4" />
                <AlertDescription>
                  These settings control how the AI automation system operates. Changes take effect immediately.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="treks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PenTool className="h-5 w-5" />
                AI Trek Package Generation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="trek-name">Trek Name</Label>
                    <Input
                      id="trek-name"
                      value={trekRequest.name}
                      onChange={(e) => setTrekRequest(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Everest Base Camp Trek"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="trek-region">Region</Label>
                    <Select value={trekRequest.region} onValueChange={(value) => setTrekRequest(prev => ({ ...prev, region: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select region" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="everest">Everest Region</SelectItem>
                        <SelectItem value="annapurna">Annapurna Region</SelectItem>
                        <SelectItem value="langtang">Langtang Region</SelectItem>
                        <SelectItem value="manaslu">Manaslu Region</SelectItem>
                        <SelectItem value="dolpo">Dolpo Region</SelectItem>
                        <SelectItem value="mustang">Mustang Region</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="trek-difficulty">Difficulty Level</Label>
                    <Select value={trekRequest.difficulty} onValueChange={(value) => setTrekRequest(prev => ({ ...prev, difficulty: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easy">Easy</SelectItem>
                        <SelectItem value="moderate">Moderate</SelectItem>
                        <SelectItem value="challenging">Challenging</SelectItem>
                        <SelectItem value="strenuous">Strenuous</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="trek-duration">Duration (days)</Label>
                    <Input
                      id="trek-duration"
                      type="number"
                      value={trekRequest.duration}
                      onChange={(e) => setTrekRequest(prev => ({ ...prev, duration: Number(e.target.value) }))}
                      min="1"
                      max="30"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="trek-altitude">Max Altitude (meters)</Label>
                    <Input
                      id="trek-altitude"
                      type="number"
                      value={trekRequest.maxAltitude}
                      onChange={(e) => setTrekRequest(prev => ({ ...prev, maxAltitude: Number(e.target.value) }))}
                      min="1000"
                      max="8000"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="trek-season">Best Season</Label>
                    <Select value={trekRequest.season} onValueChange={(value) => setTrekRequest(prev => ({ ...prev, season: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="spring">Spring (Mar-May)</SelectItem>
                        <SelectItem value="autumn">Autumn (Sep-Nov)</SelectItem>
                        <SelectItem value="winter">Winter (Dec-Feb)</SelectItem>
                        <SelectItem value="monsoon">Monsoon (Jun-Aug)</SelectItem>
                        <SelectItem value="year-round">Year Round</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4">
                <Button
                  onClick={handleGenerateTrek}
                  disabled={isGeneratingTrek || !trekRequest.name || !trekRequest.region}
                  size="lg"
                >
                  {isGeneratingTrek ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Trek Package...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Generate Trek Package
                    </>
                  )}
                </Button>
                <div className="text-sm text-muted-foreground">
                  {isGeneratingTrek ? 'AI is creating a comprehensive trek package...' : 'Ready to generate'}
                </div>
              </div>

              <Alert>
                <Brain className="h-4 w-4" />
                <AlertDescription>
                  AI will generate a complete trek package including detailed itinerary, descriptions, pricing, and all necessary information based on your specifications.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Content Calendar
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contentCalendar.length > 0 ? (
                  <div className="space-y-2">
                    {contentCalendar.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{item.topic}</h4>
                            <Badge variant={item.priority === 'high' ? 'destructive' : item.priority === 'medium' ? 'default' : 'secondary'}>
                              {item.priority}
                            </Badge>
                            <Badge variant="outline">{item.status}</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            Scheduled: {item.scheduledDate.toLocaleDateString()}
                          </p>
                          <div className="flex gap-1 mt-2">
                            {item.keywords.slice(0, 3).map((keyword, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {keyword}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No scheduled content</h3>
                    <p className="text-muted-foreground mb-4">Content calendar will be populated automatically based on AI analysis</p>
                    <Button onClick={() => loadContentCalendar()}>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Refresh Calendar
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Views</p>
                    <p className="text-2xl font-bold">{analyticsData.totalViews.toLocaleString()}</p>
                  </div>
                  <Eye className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Organic Traffic</p>
                    <p className="text-2xl font-bold">{analyticsData.organicTraffic.toLocaleString()}</p>
                  </div>
                  <Search className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Conversion Rate</p>
                    <p className="text-2xl font-bold">{analyticsData.conversionRate}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Top Performing Content</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.topPerformingContent.map((content, index) => (
                  <div key={content.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{content.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {content.views.toLocaleString()} views • {content.conversions} conversions
                        </p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivity.length > 0 ? (
                  recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center gap-3 p-2 rounded-lg border">
                      <div className={`w-2 h-2 rounded-full ${
                        activity.status === 'success' ? 'bg-green-500' :
                        activity.status === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                      }`} />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.description}</p>
                        <p className="text-xs text-muted-foreground">
                          {activity.timestamp.toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={activity.status === 'success' ? 'default' : activity.status === 'error' ? 'destructive' : 'secondary'}>
                        {activity.status}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Activity className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No recent activity</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
