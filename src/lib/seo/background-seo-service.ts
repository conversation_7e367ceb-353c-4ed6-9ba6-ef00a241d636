/**
 * Background SEO Optimization Service
 * Automatically optimizes all website content in the background
 */

import { GeminiService, createGeminiService } from './gemini-service';
import { supabase } from '@/integrations/supabase/client';

export interface SEOOptimizationJob {
  id: string;
  type: 'blog_post' | 'trek_package' | 'page_content';
  contentId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  priority: number;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
}

export interface OptimizationResult {
  contentId: string;
  originalTitle: string;
  optimizedTitle: string;
  originalExcerpt: string;
  optimizedExcerpt: string;
  suggestedTags: string[];
  seoScore: number;
  improvements: string[];
  applied: boolean;
}

export class BackgroundSEOService {
  private geminiService: GeminiService;
  private optimizationQueue: SEOOptimizationJob[] = [];
  private isProcessing: boolean = false;
  private processingInterval: number | null = null;

  constructor() {
    this.geminiService = createGeminiService();
    this.startBackgroundProcessing();
  }

  /**
   * Start background processing of SEO optimization jobs
   */
  private startBackgroundProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    // Process queue every 30 seconds
    this.processingInterval = setInterval(() => {
      if (!this.isProcessing && this.optimizationQueue.length > 0) {
        this.processNextJob();
      }
    }, 30000) as unknown as number;

    console.log('🔄 Background SEO service started');
  }

  /**
   * Stop background processing
   */
  stopBackgroundProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    console.log('⏹️ Background SEO service stopped');
  }

  /**
   * Add content to optimization queue
   */
  async queueOptimization(
    type: 'blog_post' | 'trek_package' | 'page_content',
    contentId: string,
    priority: number = 5
  ): Promise<string> {
    const job: SEOOptimizationJob = {
      id: `seo_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type,
      contentId,
      status: 'pending',
      priority,
      createdAt: new Date()
    };

    this.optimizationQueue.push(job);
    this.optimizationQueue.sort((a, b) => a.priority - b.priority); // Lower number = higher priority

    console.log(`📋 Added ${type} ${contentId} to SEO optimization queue (priority: ${priority})`);
    return job.id;
  }

  /**
   * Process the next job in the queue
   */
  private async processNextJob() {
    if (this.isProcessing || this.optimizationQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const job = this.optimizationQueue.shift()!;
    job.status = 'processing';

    console.log(`🔄 Processing SEO optimization job: ${job.id} (${job.type})`);

    try {
      const result = await this.optimizeContent(job);
      job.status = 'completed';
      job.completedAt = new Date();

      console.log(`✅ SEO optimization completed for ${job.type} ${job.contentId}`);

      // Apply optimizations automatically if they meet quality criteria
      if (result.seoScore > 7) {
        await this.applyOptimizations(result);
      }
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date();

      console.error(`❌ SEO optimization failed for ${job.type} ${job.contentId}:`, error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Optimize content based on job type
   */
  private async optimizeContent(job: SEOOptimizationJob): Promise<OptimizationResult> {
    switch (job.type) {
      case 'blog_post':
        return this.optimizeBlogPost(job.contentId);
      case 'trek_package':
        return this.optimizeTrekPackage(job.contentId);
      case 'page_content':
        return this.optimizePageContent(job.contentId);
      default:
        throw new Error(`Unknown job type: ${job.type}`);
    }
  }

  /**
   * Optimize a blog post
   */
  private async optimizeBlogPost(contentId: string): Promise<OptimizationResult> {
    console.log(`📝 Optimizing blog post: ${contentId}`);

    // Get blog post data
    const { data: post, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('id', contentId)
      .single();

    if (error || !post) {
      throw new Error(`Blog post not found: ${contentId}`);
    }

    const optimizationPrompt = `Optimize this Nepal adventure tourism blog post for SEO:

Title: ${post.title}
Content: ${post.content?.substring(0, 2000)}...
Current Excerpt: ${post.excerpt || 'No excerpt'}
Current Tags: ${post.tags || 'No tags'}

Target Audience: International adventure travelers interested in Nepal trekking and tourism
Focus: Nepal adventure tourism, trekking, cultural experiences, travel planning

Provide SEO optimizations that will improve search rankings and click-through rates.

Return ONLY a valid JSON object:
{
  "optimizedTitle": "SEO-optimized title (max 60 characters)",
  "optimizedExcerpt": "Compelling meta description (max 160 characters)",
  "suggestedTags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "seoScore": 8.5,
  "improvements": ["improvement1", "improvement2", "improvement3"]
}

Focus on Nepal-specific keywords and adventure tourism terms.`;

    try {
      const response = await this.geminiService.generateContent(optimizationPrompt, {
        temperature: 0.3,
        maxOutputTokens: 1024
      });

      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in optimization response');
      }

      const optimization = JSON.parse(jsonMatch[0]);

      return {
        contentId,
        originalTitle: post.title,
        optimizedTitle: optimization.optimizedTitle,
        originalExcerpt: post.excerpt || '',
        optimizedExcerpt: optimization.optimizedExcerpt,
        suggestedTags: optimization.suggestedTags,
        seoScore: optimization.seoScore,
        improvements: optimization.improvements,
        applied: false
      };
    } catch (error) {
      throw new Error(`Blog post optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Optimize a trek package
   */
  private async optimizeTrekPackage(contentId: string): Promise<OptimizationResult> {
    console.log(`🥾 Optimizing trek package: ${contentId}`);

    // Get trek package data
    const { data: trek, error } = await supabase
      .from('trek_packages')
      .select('*')
      .eq('id', contentId)
      .single();

    if (error || !trek) {
      throw new Error(`Trek package not found: ${contentId}`);
    }

    const optimizationPrompt = `Optimize this Nepal trek package for SEO:

Title: ${trek.name}
Description: ${trek.short_description?.substring(0, 500)}...
Long Description: ${trek.long_description?.substring(0, 1000)}...
Duration: ${trek.duration_days} days
Difficulty: ${trek.difficulty_level}
Max Altitude: ${trek.max_altitude}m
Price: $${trek.price_usd}
Region: ${trek.region}
Best Season: ${trek.best_season}

Target Audience: International trekkers looking for Nepal adventure packages
Focus: Trek-specific keywords, difficulty levels, duration, destinations

Return ONLY a valid JSON object:
{
  "optimizedTitle": "SEO-optimized trek title (max 60 characters)",
  "optimizedExcerpt": "Compelling trek description (max 160 characters)",
  "suggestedTags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "seoScore": 8.0,
  "improvements": ["improvement1", "improvement2", "improvement3"]
}

Include trek-specific terms like difficulty, duration, and destination names.`;

    try {
      const response = await this.geminiService.generateContent(optimizationPrompt, {
        temperature: 0.3,
        maxOutputTokens: 1024
      });

      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in trek optimization response');
      }

      const optimization = JSON.parse(jsonMatch[0]);

      return {
        contentId,
        originalTitle: trek.name,
        optimizedTitle: optimization.optimizedTitle,
        originalExcerpt: trek.short_description?.substring(0, 160) || '',
        optimizedExcerpt: optimization.optimizedExcerpt,
        suggestedTags: optimization.suggestedTags,
        seoScore: optimization.seoScore,
        improvements: optimization.improvements,
        applied: false
      };
    } catch (error) {
      throw new Error(`Trek package optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Optimize page content (placeholder for future implementation)
   */
  private async optimizePageContent(contentId: string): Promise<OptimizationResult> {
    console.log(`📄 Optimizing page content: ${contentId}`);

    // This would be implemented for static pages, landing pages, etc.
    throw new Error('Page content optimization not yet implemented');
  }

  /**
   * Apply optimizations to the database
   */
  private async applyOptimizations(result: OptimizationResult): Promise<void> {
    console.log(`💾 Applying SEO optimizations for content: ${result.contentId}`);

    try {
      // Determine table based on content ID pattern or type
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(result.contentId);

      if (isUUID) {
        // Try blog posts first
        const { data: blogPost } = await supabase
          .from('blog_posts')
          .select('id')
          .eq('id', result.contentId)
          .single();

        if (blogPost) {
          // Update blog post
          const { error } = await supabase
            .from('blog_posts')
            .update({
              title: result.optimizedTitle,
              excerpt: result.optimizedExcerpt,
              tags: result.suggestedTags.join(', '),
              updated_at: new Date().toISOString()
            })
            .eq('id', result.contentId);

          if (error) throw error;
          console.log(`✅ Blog post optimizations applied: ${result.contentId}`);
          return;
        }

        // Try trek packages
        const { data: trek } = await supabase
          .from('trek_packages')
          .select('id')
          .eq('id', result.contentId)
          .single();

        if (trek) {
          // Update trek package
          const { error } = await supabase
            .from('trek_packages')
            .update({
              name: result.optimizedTitle,
              short_description: result.optimizedExcerpt,
              updated_at: new Date().toISOString()
            })
            .eq('id', result.contentId);

          if (error) throw error;
          console.log(`✅ Trek package optimizations applied: ${result.contentId}`);
          return;
        }
      }

      throw new Error(`Content not found for optimization: ${result.contentId}`);
    } catch (error) {
      console.error(`❌ Failed to apply optimizations for ${result.contentId}:`, error);
      throw error;
    }
  }

  /**
   * Queue all existing content for optimization
   */
  async optimizeAllContent(): Promise<number> {
    console.log('🚀 Queuing all content for SEO optimization...');

    let queuedCount = 0;

    try {
      // Queue all blog posts
      const { data: blogPosts, error: blogError } = await supabase
        .from('blog_posts')
        .select('id')
        .order('created_at', { ascending: false });

      if (blogError) throw blogError;

      for (const post of blogPosts || []) {
        await this.queueOptimization('blog_post', post.id, 5);
        queuedCount++;
      }

      // Queue all trek packages
      const { data: treks, error: trekError } = await supabase
        .from('trek_packages')
        .select('id')
        .order('created_at', { ascending: false });

      if (trekError) throw trekError;

      for (const trek of treks || []) {
        await this.queueOptimization('trek_package', trek.id, 3); // Higher priority for treks
        queuedCount++;
      }

      console.log(`✅ Queued ${queuedCount} items for SEO optimization`);
      return queuedCount;
    } catch (error) {
      console.error('❌ Failed to queue content for optimization:', error);
      throw error;
    }
  }

  /**
   * Get optimization queue status
   */
  getQueueStatus(): {
    totalJobs: number;
    pendingJobs: number;
    processingJobs: number;
    completedJobs: number;
    failedJobs: number;
    isProcessing: boolean;
  } {
    const pending = this.optimizationQueue.filter(job => job.status === 'pending').length;
    const processing = this.optimizationQueue.filter(job => job.status === 'processing').length;
    const completed = this.optimizationQueue.filter(job => job.status === 'completed').length;
    const failed = this.optimizationQueue.filter(job => job.status === 'failed').length;

    return {
      totalJobs: this.optimizationQueue.length,
      pendingJobs: pending,
      processingJobs: processing,
      completedJobs: completed,
      failedJobs: failed,
      isProcessing: this.isProcessing
    };
  }

  /**
   * Clear completed and failed jobs from queue
   */
  clearCompletedJobs(): number {
    const initialLength = this.optimizationQueue.length;
    this.optimizationQueue = this.optimizationQueue.filter(
      job => job.status === 'pending' || job.status === 'processing'
    );
    const clearedCount = initialLength - this.optimizationQueue.length;
    console.log(`🧹 Cleared ${clearedCount} completed/failed jobs from queue`);
    return clearedCount;
  }
}

// Export singleton instance
export const backgroundSEOService = new BackgroundSEOService();
