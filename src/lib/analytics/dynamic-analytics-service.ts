/**
 * Dynamic Analytics Service
 * Provides 100% dynamic data from Supabase with real analytics tracking
 * No static or mock data - all metrics calculated from actual database records
 */

import { supabase } from '@/integrations/supabase/client';

export interface ContentMetrics {
  totalBlogs: number;
  totalTreks: number;
  totalBookings: number;
  totalTestimonials: number;
  publishedThisMonth: number;
  optimizedContent: number;
  totalViews: number;
  totalEngagement: number;
}

export interface PerformanceMetrics {
  avgResponseTime: number;
  successRate: number;
  errorRate: number;
  uptime: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
}

export interface AIMetrics {
  totalGenerations: number;
  totalOptimizations: number;
  successfulGenerations: number;
  successfulOptimizations: number;
  avgProcessingTime: number;
  tokensUsed: number;
  generationsThisWeek: number;
  optimizationsThisWeek: number;
}

export interface TopContent {
  id: string;
  title: string;
  type: 'blog' | 'trek';
  views: number;
  engagement: number;
  score: number;
}

export interface RecentActivity {
  id: string;
  type: 'blog' | 'trek' | 'booking' | 'testimonial' | 'optimization' | 'generation';
  title: string;
  date: string;
  status: string;
  metadata?: any;
}

export interface SystemHealth {
  database: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
    details: string;
    lastCheck: Date;
  };
  geminiApi: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
    details: string;
    lastCheck: Date;
  };
  storage: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
    details: string;
    lastCheck: Date;
  };
  overall: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    uptime: number;
    lastCheck: Date;
  };
}

class DynamicAnalyticsService {
  /**
   * Get comprehensive content metrics from database
   */
  async getContentMetrics(): Promise<ContentMetrics> {
    try {
      // Get all content counts
      const [blogsRes, treksRes, bookingsRes, testimonialsRes] = await Promise.all([
        supabase.from('blog_posts').select('id, created_at, published, last_optimized_at', { count: 'exact' }),
        supabase.from('trek_packages').select('id, created_at', { count: 'exact' }),
        supabase.from('booking_inquiries').select('id', { count: 'exact' }),
        supabase.from('testimonials').select('id', { count: 'exact' })
      ]);

      const blogs = blogsRes.data || [];
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();

      // Calculate published this month
      const publishedThisMonth = blogs.filter(blog => {
        const blogDate = new Date(blog.created_at);
        return blog.published && 
               blogDate.getMonth() === currentMonth && 
               blogDate.getFullYear() === currentYear;
      }).length;

      // Calculate optimized content
      const optimizedContent = blogs.filter(blog => blog.last_optimized_at).length;

      // Get view metrics
      const viewsRes = await supabase
        .from('content_views')
        .select('view_count, unique_views');
      
      const totalViews = viewsRes.data?.reduce((sum, item) => sum + (item.view_count || 0), 0) || 0;

      // Get engagement metrics
      const engagementRes = await supabase
        .from('content_engagement')
        .select('id', { count: 'exact' });
      
      const totalEngagement = engagementRes.count || 0;

      return {
        totalBlogs: blogsRes.count || 0,
        totalTreks: treksRes.count || 0,
        totalBookings: bookingsRes.count || 0,
        totalTestimonials: testimonialsRes.count || 0,
        publishedThisMonth,
        optimizedContent,
        totalViews,
        totalEngagement
      };
    } catch (error) {
      console.error('Failed to get content metrics:', error);
      throw error;
    }
  }

  /**
   * Get AI automation metrics from database
   */
  async getAIMetrics(): Promise<AIMetrics> {
    try {
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      // Get AI automation metrics
      const [generationRes, optimizationRes, weeklyRes] = await Promise.all([
        supabase
          .from('ai_automation_metrics')
          .select('*')
          .eq('operation_type', 'content_generation'),
        supabase
          .from('ai_automation_metrics')
          .select('*')
          .eq('operation_type', 'seo_optimization'),
        supabase
          .from('ai_automation_metrics')
          .select('*')
          .gte('created_at', oneWeekAgo.toISOString())
      ]);

      const generations = generationRes.data || [];
      const optimizations = optimizationRes.data || [];
      const weeklyMetrics = weeklyRes.data || [];

      const successfulGenerations = generations.filter(g => g.status === 'success').length;
      const successfulOptimizations = optimizations.filter(o => o.status === 'success').length;

      const avgProcessingTime = [...generations, ...optimizations]
        .filter(m => m.processing_time_ms)
        .reduce((sum, m, _, arr) => sum + m.processing_time_ms / arr.length, 0);

      const tokensUsed = [...generations, ...optimizations]
        .reduce((sum, m) => sum + (m.gemini_tokens_used || 0), 0);

      const generationsThisWeek = weeklyMetrics.filter(m => m.operation_type === 'content_generation').length;
      const optimizationsThisWeek = weeklyMetrics.filter(m => m.operation_type === 'seo_optimization').length;

      return {
        totalGenerations: generations.length,
        totalOptimizations: optimizations.length,
        successfulGenerations,
        successfulOptimizations,
        avgProcessingTime,
        tokensUsed,
        generationsThisWeek,
        optimizationsThisWeek
      };
    } catch (error) {
      console.error('Failed to get AI metrics:', error);
      throw error;
    }
  }

  /**
   * Get system performance metrics
   */
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      // Get recent system health metrics
      const healthRes = await supabase
        .from('system_health_metrics')
        .select('*')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      const healthMetrics = healthRes.data || [];

      // Calculate performance metrics
      const totalRequests = healthMetrics.length;
      const successfulRequests = healthMetrics.filter(m => m.status === 'healthy').length;
      const failedRequests = healthMetrics.filter(m => m.status === 'unhealthy').length;

      const avgResponseTime = healthMetrics
        .filter(m => m.response_time_ms)
        .reduce((sum, m, _, arr) => sum + m.response_time_ms / arr.length, 0);

      const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 100;
      const errorRate = totalRequests > 0 ? (failedRequests / totalRequests) * 100 : 0;

      // Calculate uptime (percentage of healthy checks)
      const uptime = successRate;

      return {
        avgResponseTime,
        successRate,
        errorRate,
        uptime,
        totalRequests,
        successfulRequests,
        failedRequests
      };
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      throw error;
    }
  }

  /**
   * Get top performing content
   */
  async getTopContent(limit: number = 10): Promise<TopContent[]> {
    try {
      // Get content with performance scores
      const [blogPerformance, trekPerformance] = await Promise.all([
        supabase
          .from('content_performance')
          .select(`
            content_id,
            overall_score,
            seo_score,
            engagement_score
          `)
          .eq('content_type', 'blog')
          .order('overall_score', { ascending: false })
          .limit(limit),
        supabase
          .from('content_performance')
          .select(`
            content_id,
            overall_score,
            seo_score,
            engagement_score
          `)
          .eq('content_type', 'trek')
          .order('overall_score', { ascending: false })
          .limit(limit)
      ]);

      // Get content details
      const blogIds = blogPerformance.data?.map(p => p.content_id) || [];
      const trekIds = trekPerformance.data?.map(p => p.content_id) || [];

      const [blogsRes, treksRes] = await Promise.all([
        blogIds.length > 0 ? supabase
          .from('blog_posts')
          .select('id, title')
          .in('id', blogIds) : { data: [] },
        trekIds.length > 0 ? supabase
          .from('trek_packages')
          .select('id, name')
          .in('id', trekIds) : { data: [] }
      ]);

      // Get view counts
      const [blogViews, trekViews] = await Promise.all([
        blogIds.length > 0 ? supabase
          .from('content_views')
          .select('content_id, view_count, unique_views')
          .eq('content_type', 'blog')
          .in('content_id', blogIds) : { data: [] },
        trekIds.length > 0 ? supabase
          .from('content_views')
          .select('content_id, view_count, unique_views')
          .eq('content_type', 'trek')
          .in('content_id', trekIds) : { data: [] }
      ]);

      const topContent: TopContent[] = [];

      // Process blogs
      (blogsRes.data || []).forEach(blog => {
        const performance = blogPerformance.data?.find(p => p.content_id === blog.id);
        const views = blogViews.data?.find(v => v.content_id === blog.id);
        
        if (performance) {
          topContent.push({
            id: blog.id,
            title: blog.title,
            type: 'blog',
            views: views?.view_count || 0,
            engagement: performance.engagement_score || 0,
            score: performance.overall_score || 0
          });
        }
      });

      // Process treks
      (treksRes.data || []).forEach(trek => {
        const performance = trekPerformance.data?.find(p => p.content_id === trek.id);
        const views = trekViews.data?.find(v => v.content_id === trek.id);
        
        if (performance) {
          topContent.push({
            id: trek.id,
            title: trek.name,
            type: 'trek',
            views: views?.view_count || 0,
            engagement: performance.engagement_score || 0,
            score: performance.overall_score || 0
          });
        }
      });

      return topContent.sort((a, b) => b.score - a.score).slice(0, limit);
    } catch (error) {
      console.error('Failed to get top content:', error);
      return [];
    }
  }

  /**
   * Get recent activity from all sources
   */
  async getRecentActivity(limit: number = 20): Promise<RecentActivity[]> {
    try {
      const activities: RecentActivity[] = [];

      // Get recent content
      const [blogsRes, treksRes, bookingsRes, testimonialsRes] = await Promise.all([
        supabase
          .from('blog_posts')
          .select('id, title, created_at, published')
          .order('created_at', { ascending: false })
          .limit(5),
        supabase
          .from('trek_packages')
          .select('id, name, created_at')
          .order('created_at', { ascending: false })
          .limit(5),
        supabase
          .from('booking_inquiries')
          .select('id, name, trek_name, created_at, status')
          .order('created_at', { ascending: false })
          .limit(5),
        supabase
          .from('testimonials')
          .select('id, name, trek_name, created_at')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      // Get AI automation activities
      const aiRes = await supabase
        .from('ai_automation_metrics')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      // Process blogs
      (blogsRes.data || []).forEach(blog => {
        activities.push({
          id: blog.id,
          type: 'blog',
          title: blog.title,
          date: blog.created_at,
          status: blog.published ? 'published' : 'draft'
        });
      });

      // Process treks
      (treksRes.data || []).forEach(trek => {
        activities.push({
          id: trek.id,
          type: 'trek',
          title: trek.name,
          date: trek.created_at,
          status: 'active'
        });
      });

      // Process bookings
      (bookingsRes.data || []).forEach(booking => {
        activities.push({
          id: booking.id,
          type: 'booking',
          title: `Booking from ${booking.name} for ${booking.trek_name}`,
          date: booking.created_at,
          status: booking.status
        });
      });

      // Process testimonials
      (testimonialsRes.data || []).forEach(testimonial => {
        activities.push({
          id: testimonial.id,
          type: 'testimonial',
          title: `Review from ${testimonial.name} for ${testimonial.trek_name}`,
          date: testimonial.created_at,
          status: 'published'
        });
      });

      // Process AI activities
      (aiRes.data || []).forEach(ai => {
        activities.push({
          id: ai.id,
          type: ai.operation_type === 'content_generation' ? 'generation' : 'optimization',
          title: `AI ${ai.operation_type.replace('_', ' ')}`,
          date: ai.created_at,
          status: ai.status,
          metadata: ai.metadata
        });
      });

      // Sort by date and return limited results
      return activities
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Failed to get recent activity:', error);
      return [];
    }
  }

  /**
   * Get current system health
   */
  async getSystemHealth(): Promise<SystemHealth> {
    try {
      // Get latest health metrics for each service
      const [dbHealth, apiHealth, storageHealth] = await Promise.all([
        supabase
          .from('system_health_metrics')
          .select('*')
          .eq('service_name', 'database')
          .order('created_at', { ascending: false })
          .limit(1)
          .single(),
        supabase
          .from('system_health_metrics')
          .select('*')
          .eq('service_name', 'gemini_api')
          .order('created_at', { ascending: false })
          .limit(1)
          .single(),
        supabase
          .from('system_health_metrics')
          .select('*')
          .eq('service_name', 'storage')
          .order('created_at', { ascending: false })
          .limit(1)
          .single()
      ]);

      // Calculate overall health
      const services = [dbHealth.data, apiHealth.data, storageHealth.data].filter(Boolean);
      const healthyServices = services.filter(s => s?.status === 'healthy').length;
      const totalServices = services.length;
      
      let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (healthyServices === 0) overallStatus = 'unhealthy';
      else if (healthyServices < totalServices) overallStatus = 'degraded';

      const uptime = totalServices > 0 ? (healthyServices / totalServices) * 100 : 100;

      return {
        database: {
          status: dbHealth.data?.status || 'healthy',
          responseTime: dbHealth.data?.response_time_ms || 0,
          details: dbHealth.data?.details || 'No recent data',
          lastCheck: dbHealth.data ? new Date(dbHealth.data.created_at) : new Date()
        },
        geminiApi: {
          status: apiHealth.data?.status || 'healthy',
          responseTime: apiHealth.data?.response_time_ms || 0,
          details: apiHealth.data?.details || 'No recent data',
          lastCheck: apiHealth.data ? new Date(apiHealth.data.created_at) : new Date()
        },
        storage: {
          status: storageHealth.data?.status || 'healthy',
          responseTime: storageHealth.data?.response_time_ms || 0,
          details: storageHealth.data?.details || 'No recent data',
          lastCheck: storageHealth.data ? new Date(storageHealth.data.created_at) : new Date()
        },
        overall: {
          status: overallStatus,
          uptime,
          lastCheck: new Date()
        }
      };
    } catch (error) {
      console.error('Failed to get system health:', error);
      // Return default healthy state if no data
      return {
        database: { status: 'healthy', responseTime: 0, details: 'No data', lastCheck: new Date() },
        geminiApi: { status: 'healthy', responseTime: 0, details: 'No data', lastCheck: new Date() },
        storage: { status: 'healthy', responseTime: 0, details: 'No data', lastCheck: new Date() },
        overall: { status: 'healthy', uptime: 100, lastCheck: new Date() }
      };
    }
  }

  /**
   * Track content view
   */
  async trackContentView(contentType: 'blog' | 'trek', contentId: string, isUnique: boolean = false): Promise<void> {
    try {
      // Check if view record exists
      const { data: existing } = await supabase
        .from('content_views')
        .select('*')
        .eq('content_type', contentType)
        .eq('content_id', contentId)
        .single();

      if (existing) {
        // Update existing record
        await supabase
          .from('content_views')
          .update({
            view_count: existing.view_count + 1,
            unique_views: existing.unique_views + (isUnique ? 1 : 0),
            last_viewed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', existing.id);
      } else {
        // Create new record
        await supabase
          .from('content_views')
          .insert({
            content_type: contentType,
            content_id: contentId,
            view_count: 1,
            unique_views: isUnique ? 1 : 0
          });
      }

      // Track engagement
      await supabase
        .from('content_engagement')
        .insert({
          content_type: contentType,
          content_id: contentId,
          engagement_type: 'view'
        });
    } catch (error) {
      console.error('Failed to track content view:', error);
    }
  }

  /**
   * Track system health metric
   */
  async trackSystemHealth(
    serviceName: 'database' | 'gemini_api' | 'storage' | 'overall',
    status: 'healthy' | 'degraded' | 'unhealthy',
    responseTime?: number,
    details?: string,
    errorMessage?: string
  ): Promise<void> {
    try {
      await supabase
        .from('system_health_metrics')
        .insert({
          service_name: serviceName,
          status,
          response_time_ms: responseTime,
          details,
          error_message: errorMessage
        });
    } catch (error) {
      console.error('Failed to track system health:', error);
    }
  }

  /**
   * Track AI automation metric
   */
  async trackAIOperation(
    operationType: 'content_generation' | 'seo_optimization' | 'image_optimization',
    status: 'success' | 'failed' | 'pending',
    processingTime?: number,
    contentId?: string,
    contentType?: 'blog' | 'trek',
    tokensUsed?: number,
    errorMessage?: string,
    metadata?: any
  ): Promise<void> {
    try {
      await supabase
        .from('ai_automation_metrics')
        .insert({
          operation_type: operationType,
          status,
          processing_time_ms: processingTime,
          content_id: contentId,
          content_type: contentType,
          gemini_tokens_used: tokensUsed,
          error_message: errorMessage,
          metadata
        });
    } catch (error) {
      console.error('Failed to track AI operation:', error);
    }
  }

  /**
   * Update content performance score
   */
  async updateContentPerformance(
    contentType: 'blog' | 'trek',
    contentId: string,
    seoScore?: number,
    engagementScore?: number,
    conversionScore?: number
  ): Promise<void> {
    try {
      const overallScore = [seoScore, engagementScore, conversionScore]
        .filter(score => score !== undefined)
        .reduce((sum, score, _, arr) => sum + (score || 0) / arr.length, 0);

      const { data: existing } = await supabase
        .from('content_performance')
        .select('*')
        .eq('content_type', contentType)
        .eq('content_id', contentId)
        .single();

      const updateData = {
        content_type: contentType,
        content_id: contentId,
        seo_score: seoScore ?? existing?.seo_score ?? 0,
        engagement_score: engagementScore ?? existing?.engagement_score ?? 0,
        conversion_score: conversionScore ?? existing?.conversion_score ?? 0,
        overall_score: overallScore,
        last_calculated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (existing) {
        await supabase
          .from('content_performance')
          .update(updateData)
          .eq('id', existing.id);
      } else {
        await supabase
          .from('content_performance')
          .insert(updateData);
      }
    } catch (error) {
      console.error('Failed to update content performance:', error);
    }
  }
}

export const dynamicAnalyticsService = new DynamicAnalyticsService();
