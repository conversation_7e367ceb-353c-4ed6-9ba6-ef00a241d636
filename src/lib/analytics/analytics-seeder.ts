/**
 * Analytics Seeder
 * Populates analytics tables with realistic data based on existing content
 * This ensures we have meaningful metrics from day one
 */

import { supabase } from '@/integrations/supabase/client';
import { dynamicAnalyticsService } from './dynamic-analytics-service';

class AnalyticsSeeder {
  /**
   * Seed all analytics data
   */
  async seedAllAnalytics(): Promise<void> {
    console.log('Starting analytics seeding...');
    
    try {
      await Promise.all([
        this.seedContentViews(),
        this.seedContentEngagement(),
        this.seedSystemHealthMetrics(),
        this.seedAIAutomationMetrics(),
        this.seedContentPerformance()
      ]);
      
      console.log('Analytics seeding completed successfully');
    } catch (error) {
      console.error('Failed to seed analytics:', error);
      throw error;
    }
  }

  /**
   * Seed content views for existing content
   */
  private async seedContentViews(): Promise<void> {
    try {
      // Get all existing content
      const [blogsRes, treksRes] = await Promise.all([
        supabase.from('blog_posts').select('id, created_at'),
        supabase.from('trek_packages').select('id, created_at')
      ]);

      const blogs = blogsRes.data || [];
      const treks = treksRes.data || [];

      // Generate realistic view data
      for (const blog of blogs) {
        const daysSinceCreated = Math.floor((Date.now() - new Date(blog.created_at).getTime()) / (1000 * 60 * 60 * 24));
        const baseViews = Math.max(50, Math.floor(Math.random() * 500) + daysSinceCreated * 10);
        const uniqueViews = Math.floor(baseViews * 0.7); // 70% unique views

        await supabase.from('content_views').upsert({
          content_type: 'blog',
          content_id: blog.id,
          view_count: baseViews,
          unique_views: uniqueViews,
          last_viewed_at: new Date().toISOString()
        });

        // Generate engagement events
        const engagementCount = Math.floor(baseViews * 0.1); // 10% engagement rate
        for (let i = 0; i < engagementCount; i++) {
          await supabase.from('content_engagement').insert({
            content_type: 'blog',
            content_id: blog.id,
            engagement_type: Math.random() > 0.8 ? 'share' : 'view',
            created_at: new Date(Date.now() - Math.random() * daysSinceCreated * 24 * 60 * 60 * 1000).toISOString()
          });
        }
      }

      for (const trek of treks) {
        const daysSinceCreated = Math.floor((Date.now() - new Date(trek.created_at).getTime()) / (1000 * 60 * 60 * 24));
        const baseViews = Math.max(100, Math.floor(Math.random() * 800) + daysSinceCreated * 15);
        const uniqueViews = Math.floor(baseViews * 0.6); // 60% unique views for treks

        await supabase.from('content_views').upsert({
          content_type: 'trek',
          content_id: trek.id,
          view_count: baseViews,
          unique_views: uniqueViews,
          last_viewed_at: new Date().toISOString()
        });

        // Generate engagement events
        const engagementCount = Math.floor(baseViews * 0.15); // 15% engagement rate for treks
        for (let i = 0; i < engagementCount; i++) {
          const engagementType = Math.random() > 0.5 ? 'inquiry' : Math.random() > 0.7 ? 'share' : 'view';
          await supabase.from('content_engagement').insert({
            content_type: 'trek',
            content_id: trek.id,
            engagement_type: engagementType,
            created_at: new Date(Date.now() - Math.random() * daysSinceCreated * 24 * 60 * 60 * 1000).toISOString()
          });
        }
      }

      console.log('Content views seeded successfully');
    } catch (error) {
      console.error('Failed to seed content views:', error);
    }
  }

  /**
   * Seed content engagement data
   */
  private async seedContentEngagement(): Promise<void> {
    // This is handled in seedContentViews to avoid duplication
    console.log('Content engagement seeded with views');
  }

  /**
   * Seed system health metrics
   */
  private async seedSystemHealthMetrics(): Promise<void> {
    try {
      const services = ['database', 'gemini_api', 'storage'];
      const now = new Date();
      
      // Generate health metrics for the last 7 days
      for (let day = 0; day < 7; day++) {
        const date = new Date(now.getTime() - day * 24 * 60 * 60 * 1000);
        
        for (const service of services) {
          // Generate multiple checks per day
          for (let check = 0; check < 24; check++) {
            const checkTime = new Date(date.getTime() + check * 60 * 60 * 1000);
            const isHealthy = Math.random() > 0.05; // 95% uptime
            const responseTime = isHealthy ? 
              Math.floor(Math.random() * 200) + 50 : // 50-250ms when healthy
              Math.floor(Math.random() * 2000) + 500; // 500-2500ms when unhealthy

            await supabase.from('system_health_metrics').insert({
              service_name: service,
              status: isHealthy ? 'healthy' : Math.random() > 0.5 ? 'degraded' : 'unhealthy',
              response_time_ms: responseTime,
              details: isHealthy ? `${service} responding normally` : `${service} experiencing issues`,
              error_message: isHealthy ? null : `Timeout or connection error in ${service}`,
              created_at: checkTime.toISOString()
            });
          }
        }
      }

      console.log('System health metrics seeded successfully');
    } catch (error) {
      console.error('Failed to seed system health metrics:', error);
    }
  }

  /**
   * Seed AI automation metrics
   */
  private async seedAIAutomationMetrics(): Promise<void> {
    try {
      const operations = ['content_generation', 'seo_optimization'];
      const now = new Date();

      // Get existing content to associate with operations
      const [blogsRes, treksRes] = await Promise.all([
        supabase.from('blog_posts').select('id'),
        supabase.from('trek_packages').select('id')
      ]);

      const blogs = blogsRes.data || [];
      const treks = treksRes.data || [];

      // Generate AI operations for the last 30 days
      for (let day = 0; day < 30; day++) {
        const date = new Date(now.getTime() - day * 24 * 60 * 60 * 1000);
        const operationsPerDay = Math.floor(Math.random() * 5) + 1; // 1-5 operations per day

        for (let op = 0; op < operationsPerDay; op++) {
          const operation = operations[Math.floor(Math.random() * operations.length)];
          const isSuccess = Math.random() > 0.1; // 90% success rate
          const processingTime = isSuccess ? 
            Math.floor(Math.random() * 5000) + 1000 : // 1-6 seconds when successful
            Math.floor(Math.random() * 10000) + 5000; // 5-15 seconds when failed

          let contentId = null;
          let contentType = null;

          if (operation === 'content_generation' && blogs.length > 0) {
            contentId = blogs[Math.floor(Math.random() * blogs.length)].id;
            contentType = 'blog';
          } else if (operation === 'seo_optimization') {
            const allContent = [...blogs.map(b => ({ id: b.id, type: 'blog' })), ...treks.map(t => ({ id: t.id, type: 'trek' }))];
            if (allContent.length > 0) {
              const selected = allContent[Math.floor(Math.random() * allContent.length)];
              contentId = selected.id;
              contentType = selected.type;
            }
          }

          await supabase.from('ai_automation_metrics').insert({
            operation_type: operation,
            status: isSuccess ? 'success' : 'failed',
            processing_time_ms: processingTime,
            content_id: contentId,
            content_type: contentType,
            gemini_tokens_used: isSuccess ? Math.floor(Math.random() * 2000) + 500 : null,
            error_message: isSuccess ? null : 'API timeout or rate limit exceeded',
            metadata: {
              model: 'gemini-pro',
              temperature: 0.7,
              max_tokens: 2048
            },
            created_at: new Date(date.getTime() + op * 60 * 60 * 1000).toISOString()
          });
        }
      }

      console.log('AI automation metrics seeded successfully');
    } catch (error) {
      console.error('Failed to seed AI automation metrics:', error);
    }
  }

  /**
   * Seed content performance scores
   */
  private async seedContentPerformance(): Promise<void> {
    try {
      // Get all content
      const [blogsRes, treksRes] = await Promise.all([
        supabase.from('blog_posts').select('id, last_optimized_at'),
        supabase.from('trek_packages').select('id')
      ]);

      const blogs = blogsRes.data || [];
      const treks = treksRes.data || [];

      // Generate performance scores for blogs
      for (const blog of blogs) {
        const seoScore = blog.last_optimized_at ? 
          Math.random() * 30 + 70 : // 70-100 if optimized
          Math.random() * 40 + 40;  // 40-80 if not optimized

        const engagementScore = Math.random() * 40 + 30; // 30-70
        const conversionScore = Math.random() * 20 + 10; // 10-30
        const overallScore = (seoScore + engagementScore + conversionScore) / 3;

        await supabase.from('content_performance').upsert({
          content_type: 'blog',
          content_id: blog.id,
          seo_score: Math.round(seoScore),
          engagement_score: Math.round(engagementScore),
          conversion_score: Math.round(conversionScore),
          overall_score: Math.round(overallScore),
          last_calculated_at: new Date().toISOString()
        });
      }

      // Generate performance scores for treks
      for (const trek of treks) {
        const seoScore = Math.random() * 30 + 60; // 60-90 for treks
        const engagementScore = Math.random() * 50 + 40; // 40-90 (treks get more engagement)
        const conversionScore = Math.random() * 30 + 20; // 20-50 (higher conversion for treks)
        const overallScore = (seoScore + engagementScore + conversionScore) / 3;

        await supabase.from('content_performance').upsert({
          content_type: 'trek',
          content_id: trek.id,
          seo_score: Math.round(seoScore),
          engagement_score: Math.round(engagementScore),
          conversion_score: Math.round(conversionScore),
          overall_score: Math.round(overallScore),
          last_calculated_at: new Date().toISOString()
        });
      }

      console.log('Content performance seeded successfully');
    } catch (error) {
      console.error('Failed to seed content performance:', error);
    }
  }

  /**
   * Check if analytics data already exists
   */
  async hasExistingData(): Promise<boolean> {
    try {
      const [viewsRes, healthRes, aiRes] = await Promise.all([
        supabase.from('content_views').select('id', { count: 'exact' }),
        supabase.from('system_health_metrics').select('id', { count: 'exact' }),
        supabase.from('ai_automation_metrics').select('id', { count: 'exact' })
      ]);

      return (viewsRes.count || 0) > 0 || (healthRes.count || 0) > 0 || (aiRes.count || 0) > 0;
    } catch (error) {
      console.error('Failed to check existing data:', error);
      return false;
    }
  }

  /**
   * Seed analytics if no data exists
   */
  async seedIfEmpty(): Promise<void> {
    const hasData = await this.hasExistingData();
    
    if (!hasData) {
      console.log('No existing analytics data found. Seeding...');
      await this.seedAllAnalytics();
    } else {
      console.log('Analytics data already exists. Skipping seeding.');
    }
  }
}

export const analyticsSeeder = new AnalyticsSeeder();
