/**
 * Automated Content Generation Service
 * Uses Google Gemini API to research and create blog content automatically
 */

import { GeminiService, createGeminiService } from '../seo/gemini-service';
import { supabase } from '@/integrations/supabase/client';

export interface ContentStrategy {
  topic: string;
  targetKeywords: string[];
  contentType: 'blog' | 'guide' | 'tips' | 'destination';
  priority: 'high' | 'medium' | 'low';
  estimatedTraffic: number;
  competitionLevel: 'low' | 'medium' | 'high';
  seasonality?: string;
}

export interface GeneratedContent {
  title: string;
  content: string;
  excerpt: string;
  tags: string[];
  focusKeywords: string[];
  estimatedReadingTime: number;
  seoScore: number;
  publishReady: boolean;
}

export interface ContentResearch {
  trendingTopics: string[];
  seasonalContent: string[];
  competitorGaps: string[];
  userQuestions: string[];
  recommendedKeywords: string[];
}

export class AutomatedContentGenerator {
  private geminiService: GeminiService;
  private contentQueue: ContentStrategy[] = [];
  private isGenerating: boolean = false;

  constructor() {
    this.geminiService = createGeminiService();
  }

  /**
   * Research content opportunities for Nepal adventure tourism
   */
  async researchContentOpportunities(): Promise<ContentResearch> {
    console.log('🔍 Researching content opportunities...');

    const researchPrompt = `You are a content strategist for a Nepal adventure tourism website targeting international tourists from USA, UK, Australia, and Europe.

Research and analyze content opportunities for our website. Consider:
- Current travel trends for Nepal
- Seasonal trekking patterns
- Popular adventure activities
- Cultural experiences
- Practical travel information
- Safety and preparation guides

Return ONLY a valid JSON object with this structure:
{
  "trendingTopics": ["topic1", "topic2", "topic3", "topic4", "topic5"],
  "seasonalContent": ["seasonal1", "seasonal2", "seasonal3"],
  "competitorGaps": ["gap1", "gap2", "gap3"],
  "userQuestions": ["question1", "question2", "question3", "question4"],
  "recommendedKeywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]
}

Focus on high-value, low-competition topics that would attract adventure travelers to Nepal.`;

    try {
      const response = await this.geminiService.generateContent(researchPrompt, {
        temperature: 0.3,
        maxOutputTokens: 1024
      });

      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in research response');
      }

      const research = JSON.parse(jsonMatch[0]);
      console.log('✅ Content research completed:', research);
      return research;
    } catch (error) {
      console.error('❌ Content research failed:', error);
      throw new Error(`Content research failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate content strategy based on research
   */
  async generateContentStrategy(research: ContentResearch): Promise<ContentStrategy[]> {
    console.log('📋 Generating content strategy...');

    const strategyPrompt = `Based on this content research for a Nepal adventure tourism website:

Trending Topics: ${research.trendingTopics.join(', ')}
Seasonal Content: ${research.seasonalContent.join(', ')}
Competitor Gaps: ${research.competitorGaps.join(', ')}
User Questions: ${research.userQuestions.join(', ')}
Recommended Keywords: ${research.recommendedKeywords.join(', ')}

Create a content strategy with 5-8 high-priority blog post ideas. For each idea, provide:
- Topic (specific and engaging)
- Target keywords (3-5 keywords)
- Content type (blog, guide, tips, destination)
- Priority (high, medium, low)
- Estimated monthly traffic potential (number)
- Competition level (low, medium, high)
- Seasonality if applicable

Return ONLY a valid JSON array:
[
  {
    "topic": "Complete Guide to Everest Base Camp Trek: What to Expect in 2024",
    "targetKeywords": ["everest base camp trek", "nepal trekking guide", "himalayan adventure"],
    "contentType": "guide",
    "priority": "high",
    "estimatedTraffic": 2500,
    "competitionLevel": "medium",
    "seasonality": "spring-autumn"
  }
]

Focus on topics that will drive qualified traffic from adventure travelers.`;

    try {
      const response = await this.geminiService.generateContent(strategyPrompt, {
        temperature: 0.4,
        maxOutputTokens: 1500
      });

      const jsonMatch = response.text.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No valid JSON array found in strategy response');
      }

      const strategy = JSON.parse(jsonMatch[0]);
      console.log('✅ Content strategy generated:', strategy);
      return strategy;
    } catch (error) {
      console.error('❌ Content strategy generation failed:', error);
      throw new Error(`Strategy generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate full blog content for a given strategy
   */
  async generateBlogContent(strategy: ContentStrategy): Promise<GeneratedContent> {
    console.log('✍️ Generating blog content for:', strategy.topic);

    const contentPrompt = `You are an expert travel writer specializing in Nepal adventure tourism. Write a comprehensive, engaging blog post about: "${strategy.topic}"

Target Keywords: ${strategy.targetKeywords.join(', ')}
Content Type: ${strategy.contentType}
Target Audience: International adventure travelers from USA, UK, Australia, Europe

Requirements:
- 1500-2500 words of high-quality, informative content
- Include practical information, tips, and personal insights
- Optimize for SEO while maintaining readability
- Include specific details about Nepal (locations, costs, logistics)
- Write in an engaging, authoritative tone
- Include safety considerations and preparation tips
- Add cultural context and respect for local customs

Return ONLY a valid JSON object:
{
  "title": "SEO-optimized title (max 60 characters)",
  "content": "Full blog post content in HTML format with proper headings, paragraphs, and structure",
  "excerpt": "Compelling meta description (max 160 characters)",
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "focusKeywords": ["keyword1", "keyword2", "keyword3"],
  "estimatedReadingTime": 8,
  "seoScore": 85,
  "publishReady": true
}

Make the content authentic, helpful, and optimized for search engines.`;

    try {
      const response = await this.geminiService.generateContent(contentPrompt, {
        temperature: 0.6,
        maxOutputTokens: 4096
      });

      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in content response');
      }

      const content = JSON.parse(jsonMatch[0]);

      // Validate required fields
      if (!content.title || !content.content || !content.excerpt) {
        throw new Error('Generated content missing required fields');
      }

      console.log('✅ Blog content generated:', content.title);
      return content;
    } catch (error) {
      console.error('❌ Content generation failed:', error);
      throw new Error(`Content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save generated content to database
   */
  async saveGeneratedContent(content: GeneratedContent): Promise<string> {
    console.log('💾 Saving generated content to database...');

    try {
      // Generate a unique slug from the title
      const slug = content.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
        .substring(0, 100) + '-' + Date.now();

      const { data, error } = await supabase
        .from('blog_posts')
        .insert({
          title: content.title,
          slug: slug,
          content: content.content,
          excerpt: content.excerpt,
          tags: content.tags, // Keep as array
          published: false, // Save as draft initially
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) throw error;

      console.log('✅ Content saved with ID:', data.id);

      // Track content views and performance for analytics
      await this.initializeContentAnalytics(data.id, content);

      return data.id;
    } catch (error) {
      console.error('❌ Failed to save content:', error);
      throw new Error(`Failed to save content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Initialize analytics tracking for new content
   */
  private async initializeContentAnalytics(contentId: string, content: GeneratedContent): Promise<void> {
    try {
      // Initialize content views
      await supabase.from('content_views').insert({
        content_type: 'blog',
        content_id: contentId,
        view_count: 0,
        unique_views: 0
      });

      // Initialize content performance
      await supabase.from('content_performance').insert({
        content_type: 'blog',
        content_id: contentId,
        seo_score: content.seoScore || 0,
        engagement_score: 0,
        conversion_score: 0,
        overall_score: content.seoScore || 0
      });

      console.log('✅ Analytics initialized for content:', contentId);
    } catch (error) {
      console.error('⚠️ Failed to initialize analytics:', error);
      // Don't throw error as content was saved successfully
    }
  }

  /**
   * Generate and save multiple blog posts automatically
   */
  async generateContentBatch(count: number = 3): Promise<string[]> {
    console.log(`🚀 Starting automated content generation batch (${count} posts)...`);

    if (this.isGenerating) {
      throw new Error('Content generation already in progress');
    }

    this.isGenerating = true;
    const generatedIds: string[] = [];

    try {
      // Step 1: Research content opportunities
      const research = await this.researchContentOpportunities();

      // Step 2: Generate content strategy
      const strategies = await this.generateContentStrategy(research);

      // Step 3: Generate content for top strategies
      const topStrategies = strategies
        .sort((a, b) => {
          // Sort by priority and estimated traffic
          const priorityWeight = { high: 3, medium: 2, low: 1 };
          const aPriority = priorityWeight[a.priority];
          const bPriority = priorityWeight[b.priority];

          if (aPriority !== bPriority) {
            return bPriority - aPriority;
          }

          return b.estimatedTraffic - a.estimatedTraffic;
        })
        .slice(0, count);

      for (const strategy of topStrategies) {
        try {
          console.log(`📝 Generating content for: ${strategy.topic}`);

          const content = await this.generateBlogContent(strategy);
          const contentId = await this.saveGeneratedContent(content);

          generatedIds.push(contentId);

          // Add delay between generations to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
          console.error(`❌ Failed to generate content for ${strategy.topic}:`, error);
          // Continue with next strategy
        }
      }

      console.log(`✅ Content generation batch completed. Generated ${generatedIds.length} posts.`);
      return generatedIds;
    } catch (error) {
      console.error('❌ Content generation batch failed:', error);
      throw error;
    } finally {
      this.isGenerating = false;
    }
  }

  /**
   * Analyze existing content and suggest improvements
   */
  async analyzeExistingContent(): Promise<{
    totalPosts: number;
    needsOptimization: string[];
    contentGaps: string[];
    recommendations: string[];
  }> {
    console.log('📊 Analyzing existing content...');

    try {
      // Get existing blog posts
      const { data: posts, error } = await supabase
        .from('blog_posts')
        .select('id, title, content, excerpt, tags, created_at')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const analysisPrompt = `Analyze this Nepal adventure tourism blog content and provide recommendations:

Existing Posts:
${posts?.map(post => `- ${post.title} (${post.tags || 'No tags'})`).join('\n') || 'No posts found'}

Analyze for:
1. Content gaps in Nepal adventure tourism
2. Posts that need SEO optimization
3. Missing seasonal content
4. Opportunities for better keyword targeting

Return ONLY a valid JSON object:
{
  "totalPosts": ${posts?.length || 0},
  "needsOptimization": ["post-id-1", "post-id-2"],
  "contentGaps": ["gap1", "gap2", "gap3"],
  "recommendations": ["rec1", "rec2", "rec3"]
}`;

      const response = await this.geminiService.generateContent(analysisPrompt, {
        temperature: 0.3,
        maxOutputTokens: 1024
      });

      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in analysis response');
      }

      const analysis = JSON.parse(jsonMatch[0]);
      console.log('✅ Content analysis completed:', analysis);
      return analysis;
    } catch (error) {
      console.error('❌ Content analysis failed:', error);
      throw new Error(`Content analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if content generation is in progress
   */
  isGeneratingContent(): boolean {
    return this.isGenerating;
  }

  /**
   * Get current content queue
   */
  getContentQueue(): ContentStrategy[] {
    return [...this.contentQueue];
  }
}

// Export singleton instance
export const automatedContentGenerator = new AutomatedContentGenerator();
